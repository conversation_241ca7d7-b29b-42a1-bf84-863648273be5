import dayjs from 'dayjs'

/**
 * 根据传入的日期展示相对时间描述
 * @param {string|Date} date - 要比较的日期
 * @returns {string} 返回 '今天'、'明天'、'后天' 或 '距离今天x天'
 */
export function getRelativeDateText(date) {
  if (!date) return ''
  const targetDate = dayjs(date).startOf('day')
  const today = dayjs().startOf('day')

  // 计算天数差
  const diffDays = targetDate.diff(today, 'day')
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '明天'
  } else if (diffDays === 2) {
    return '后天'
  } else if (diffDays > 0) {
    return `距离今天${diffDays}天`
  } else {
    return `${Math.abs(diffDays)}天前`
  }
}

/**
 * 判断某个人是否是某个部门的admin
 * @param {Array} departments - 用户的部门数据数组，格式：[{"deptType":3,"isAdmin":0},{"deptType":2,"isAdmin":0}]
 * @param {number} deptType - 要检查的部门类型 (1:飞行部, 2:机务部, 3:运控中心, 4:市场部)
 * @returns {boolean} 返回 true 表示是该部门的admin，false 表示不是
 */
export function isDepartmentAdmin(departments, deptType) {
  // 参数验证
  if (!Array.isArray(departments) || typeof deptType !== 'number') {
    return false
  }

  // 查找指定部门
  const department = departments.find(dept => dept.deptType === deptType)

  // 如果找到部门且isAdmin为1，则返回true
  return department && department.isAdmin === 1
}

/**
 * 根据roleType字符串判断用户是否有某个部门的编辑权限
 * @param {Array} departments - 用户的部门数据数组
 * @param {string} roleType - 角色类型字符串，如 "1,2,3,4"
 * @returns {object} 返回各部门的编辑权限状态
 */
export function getDepartmentEditPermissions(departments, roleType) {
  if (!Array.isArray(departments) || !roleType) {
    return {
      canEditFlight: false,      // 飞行部 (deptType: 1)
      canEditMaintenance: false, // 机务部 (deptType: 2)
      canEditOperation: false,   // 运控中心 (deptType: 3)
      canEditMarket: false       // 市场部 (deptType: 4)
    }
  }

  const roleArray = roleType.split(',').map(role => parseInt(role.trim()))

  return {
    canEditFlight: roleArray.includes(1) && isDepartmentAdmin(departments, 1),
    canEditMaintenance: roleArray.includes(2) && isDepartmentAdmin(departments, 2),
    canEditOperation: roleArray.includes(3) && isDepartmentAdmin(departments, 3),
    canEditMarket: roleArray.includes(4) && isDepartmentAdmin(departments, 4)
  }
}
