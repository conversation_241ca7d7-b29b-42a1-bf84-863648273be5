import dayjs from 'dayjs'

/**
 * 根据传入的日期展示相对时间描述
 * @param {string|Date} date - 要比较的日期
 * @returns {string} 返回 '今天'、'明天'、'后天' 或 '距离今天x天'
 */
export function getRelativeDateText(date) {
  if (!date) return ''
  const targetDate = dayjs(date).startOf('day')
  const today = dayjs().startOf('day')

  // 计算天数差
  const diffDays = targetDate.diff(today, 'day')
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '明天'
  } else if (diffDays === 2) {
    return '后天'
  } else if (diffDays > 0) {
    return `距离今天${diffDays}天`
  } else {
    return `${Math.abs(diffDays)}天前`
  }
}
