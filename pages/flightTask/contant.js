import dayjs from 'dayjs'

/**
 * @description: 计算是否今天明天
 */
export const transferDateText = (date) => {
  const diffDays = dayjs(date).diff(dayjs(), 'day')
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '明天'
  } else {
    return '其他日期'
  }
}

export const isDepAdmin = (depAuth) => {
  const dep = JSON.parse(uni.getStorageSync('departments') || '[]')
  if (!auth) return false
  dep.map((item) => {
    if (item.deptType === depAuth) {
      return item.deptAdmin
    }
  })
}
