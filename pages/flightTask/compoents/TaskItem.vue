<template>
  <view class="task-card">
    <view class="task-header">
      <view class="title-box"> {{ taskItem.taskType }}</view>
      <view class="option-box" v-if="pageType === 1" @click="handleTaskClick()">
        <text>去确认</text>
        <van-icon name="arrow" />
      </view>
      <view class="option-box" v-if="pageType === 2">
        <view @click="handleEditClick()">
          <van-icon name="edit" size="12" />
          <text>修改</text>
        </view>
        <view @click="handleDeleteClick()" v-if="pageType === 2">
          <van-icon name="delete" size="12" />
          <text>删除</text>
        </view>
      </view>
    </view>
    <view class="task-content">
      <view class="info-row">
        <view class="info-item">
          <text class="label">注册号：</text>
          <text class="value">{{ taskItem.registrationNumber }}</text>
        </view>
        <view class="info-item">
          <text class="label">机型：</text>
          <text class="value">{{ taskItem.aircraftType }}</text>
        </view>
      </view>

      <view class="info-row" v-if="taskItem.departure">
        <view class="info-item">
          <text class="label">起飞基地：</text>
          <text class="value">{{ taskItem.departure }}</text>
        </view>
      </view>
      <view class="info-row" v-if="taskItem.routeOrAirspaceName">
        <view class="info-item full-width">
          <text class="label">航线/空域：</text>
          <text class="value">{{ taskItem.routeOrAirspaceName }}</text>
        </view>
      </view>
      <view class="info-row">
        <view class="info-item full-width">
          <text class="label">飞行日期：</text>
          <text class="value"
            >{{ taskItem.flightDate || '' }}({{
              getRelativeDateText(taskItem.flightDate)
            }})
          </text>
        </view>
      </view>

      <view class="info-row">
        <view class="info-item">
          <text class="label">预估架次：</text>
          <text class="value">{{ taskItem.flightFrequency }}</text>
        </view>
      </view>

      <view class="info-row">
        <view class="info-item">
          <text class="label">创建人：</text>
          <text class="value">{{ taskItem.createBy }}</text>
        </view>
      </view>

      <view class="info-row">
        <view class="info-item full-width">
          <text class="label">发布时间：</text>
          <text class="value">{{ taskItem.createTime }}</text>
        </view>
      </view>
      <view class="split-line"></view>
      <view class="info-row">
        <view class="info-item full-width">
          <text class="label">已确认部门：</text>
          <view class="value process-box">
            <view
              v-for="item in processList"
              :key="item.name"
              class="process-item"
              :class="processStatusClassName[item.statusName]"
            >
              <view class="circle"></view>
              <text class="name">{{ item.name }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getRelativeDateText } from '../../../utils'
import Dialog from '../../../wxcomponents/vant/dialog/dialog'
import { queryFlightTaskConfigDetail } from '../../../api/flightTask'

export default {
  name: 'TaskItem',
  props: {
    taskItem: {
      type: Object,
    },
    pageType: {
      type: Number,
    },
  },
  data() {
    return {
      processList: [
        { name: '市场', statusName: 'marketConfirm' },
        { name: '飞行', statusName: 'flightConfirm' },
        { name: '机务', statusName: 'maintenanceConfirm' },
        { name: '运控', statusName: 'ocConfirm' },
      ],
      processStatusClassName: {
        0: '',
        1: 'active',
        2: 'save',
      },
    }
  },
  methods: {
    getRelativeDateText,
    handleTaskClick() {
      uni.navigateTo({
        url: `/pages/flightTask/receiveDetail?id=${this.taskItem.id}`,
      })
    },
    //修改
    handleEditClick() {
      uni.navigateTo({
        url: `/pages/flightTask/create?id=${this.taskItem.id}`,
      })
    },
    //删除
    handleDeleteClick() {
      Dialog.confirm({
        title: '删除确认',
        message: '是否确认删除该任务',
        beforeClose: async (action, done) => {
          if (action === 'confirm') {
            const res = await queryFlightTaskConfigDetail({
              flightTaskConfigId: this.taskItem.id,
            })
            if (res.response.code === 200) {
              uni.showToast({
                title: res.response.msg || '删除成功！',
              })
              this.$emit('refresh')
              // 关闭弹窗
              done()
            } else {
              // 接口失败，不关闭弹窗
              done(false)
            }
          } else {
            // 拦截取消操作（不关闭弹窗）
            done(false)
          }
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
@import '../../../assets/css/common.less';

.task-card {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.task-header {
  border-radius: 8px 8px 0 0;
  background: #2c5de5;
  color: #fff;
  box-sizing: border-box;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-box {
    font-weight: bold;
  }

  .option-box {
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    opacity: 0.9;
    gap: 8px;

    view {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.task-content {
  padding: 16px;
  box-sizing: border-box;
}

.split-line {
  width: 100%;
  height: 1px;
  margin: 12px 0;
  background: #f0f0f0;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 50%;
}

.info-item.full-width {
  flex: 1;
  min-width: 100%;
}

.label {
  color: #666666;
  font-size: 14px;
  margin-right: 4px;
  white-space: nowrap;
}

.value {
  color: #333333;
  font-size: 14px;
  font-weight: 500;
  //white-space: wrap;
  word-break: break-all; /* 强制在单词内换行 */
  white-space: pre-line;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; //行数
  -webkit-box-orient: vertical;
}

.process-box {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 0;

  .process-item {
    text-align: center;

    .circle {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #bec0c5;
      margin: 0 auto 2px auto;
    }

    .name {
      font-size: 14px;
      color: #bec0c5;
      font-weight: normal;
    }

    &.active {
      .circle {
        //border: 2px solid #008556;
        background: #07c160;
      }

      .name {
        color: #07c160;
      }
    }

    &.save {
      .circle {
        background: #ecbf2f;
      }

      .name {
        color: #ecbf2f;
      }
    }

    &:last-child {
      .connect-line {
        display: none;
      }
    }
  }
}
</style>
